# LLM Provider Migration Plan

## Översikt

Denna plan beskriver hur vi migrerar från hårdkodad OpenAI integration till en flexibel LLM provider-arkitektur som stöder enkelt byte mellan providers (OpenAI, Azure OpenAI) och modeller (gpt-4o-mini, o1-mini, etc.).

## Nuvarande Situation

### Befintliga komponenter som använder OpenAI:
- `backend/src/api/ai-assistant.ts` - AI Assistant API endpoints
- `backend/src/runners/ai/AIRunner.ts` - AI Runner för RPA steps  
- `backend/src/runners/api/APIRunner.ts` - API Runner (Fortnox integration)
- `backend/src/runners/ai/stepExecutors/llmProcessing.ts` - LLM processing steps

### Problem med nuvarande implementation:
- Hårdkodad OpenAI client instantiering
- Hårdkodad modell `gpt-4o-mini` på flera ställen
- Ingen abstraktion för olika providers
- Svårt att byta modell eller provider

## Målarkitektur

### 1. LLM Provider Interface

```typescript
interface LLMProvider {
  name: string;
  isConfigured(): boolean;
  getSupportedModels(): string[];
  createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  testConnection(): Promise<boolean>;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
}

interface ChatCompletionResponse {
  content: string;
  usage?: TokenUsage;
}
```

### 2. Model Registry System

```typescript
interface ModelConfig {
  id: string;           // Internt ID: "gpt-4o-mini", "o1-mini"
  displayName: string;  // Visningsnamn: "GPT-4o Mini", "o1 Mini"
  provider: string;     // "openai" | "azure"
  providerModel: string; // Provider-specifikt namn/deployment
  maxTokens: number;
  supportsStreaming: boolean;
  costPer1kTokens: number;
}
```

### 3. Konfigurationssystem

```bash
# LLM Provider Configuration
LLM_PROVIDER=azure                    # 'openai' | 'azure'
LLM_DEFAULT_MODEL=gpt-4o-mini        # Standardmodell för alla AI-anrop
LLM_FALLBACK_MODEL=gpt-4o-mini       # Fallback om default inte fungerar

# OpenAI Configuration  
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Model Deployments (Azure-specifikt)
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
AZURE_MODEL_O1_MINI=o1-mini-deployment
```

## Detaljerad Implementation Plan

### Steg 1: Skapa Provider Interface och Base Classes

**Fil: `backend/src/services/llm/interfaces.ts`**
```typescript
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
}

export interface ChatCompletionResponse {
  content: string;
  usage?: TokenUsage;
}

export interface LLMProvider {
  name: string;
  isConfigured(): boolean;
  getSupportedModels(): string[];
  createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  testConnection(): Promise<boolean>;
}
```

**Fil: `backend/src/services/llm/BaseLLMProvider.ts`**
```typescript
import { LLMProvider, ChatCompletionRequest, ChatCompletionResponse } from './interfaces';

export abstract class BaseLLMProvider implements LLMProvider {
  abstract name: string;
  
  abstract isConfigured(): boolean;
  abstract getSupportedModels(): string[];
  abstract createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  
  async testConnection(): Promise<boolean> {
    try {
      await this.createChatCompletion({
        model: this.getSupportedModels()[0],
        messages: [{ role: 'user', content: 'test' }],
        maxTokens: 1
      });
      return true;
    } catch {
      return false;
    }
  }
}
```

### Steg 2: Implementera Model Registry

**Fil: `backend/src/services/llm/ModelRegistry.ts`**
```typescript
export interface ModelConfig {
  id: string;
  displayName: string;
  provider: string;
  providerModel: string;
  maxTokens: number;
  supportsStreaming: boolean;
  costPer1kTokens: number;
}

export class ModelRegistry {
  private static models: ModelConfig[] = [
    {
      id: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini',
      provider: 'openai',
      providerModel: 'gpt-4o-mini',
      maxTokens: 128000,
      supportsStreaming: true,
      costPer1kTokens: 0.00015
    },
    {
      id: 'o1-mini',
      displayName: 'o1 Mini',
      provider: 'openai', 
      providerModel: 'o1-mini',
      maxTokens: 65536,
      supportsStreaming: false,
      costPer1kTokens: 0.003
    },
    {
      id: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini (Azure)',
      provider: 'azure',
      providerModel: process.env.AZURE_MODEL_GPT4O_MINI || 'gpt-4o-mini',
      maxTokens: 128000,
      supportsStreaming: true,
      costPer1kTokens: 0.00015
    }
  ];

  static getModel(id: string, provider?: string): ModelConfig | undefined {
    return this.models.find(m => 
      m.id === id && (provider ? m.provider === provider : true)
    );
  }

  static getModelsForProvider(provider: string): ModelConfig[] {
    return this.models.filter(m => m.provider === provider);
  }

  static getAllModels(): ModelConfig[] {
    return [...this.models];
  }
}
```

### Steg 3: Implementera OpenAI Provider

**Fil: `backend/src/services/llm/providers/OpenAIProvider.ts`**
```typescript
import OpenAI from 'openai';
import { BaseLLMProvider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class OpenAIProvider extends BaseLLMProvider {
  name = 'openai';
  private client: OpenAI;

  constructor() {
    super();
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  isConfigured(): boolean {
    return !!process.env.OPENAI_API_KEY;
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('openai').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const modelConfig = ModelRegistry.getModel(request.model, 'openai');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for OpenAI provider`);
    }

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
```

### Steg 4: Implementera Azure OpenAI Provider

**Fil: `backend/src/services/llm/providers/AzureOpenAIProvider.ts`**
```typescript
import { OpenAI } from 'openai';
import { BaseLLMProvider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class AzureOpenAIProvider extends BaseLLMProvider {
  name = 'azure';
  private client: OpenAI;

  constructor() {
    super();

    const requiredEnvVars = [
      'AZURE_OPENAI_API_KEY',
      'AZURE_OPENAI_ENDPOINT',
      'AZURE_OPENAI_API_VERSION'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`${envVar} environment variable is required for Azure OpenAI`);
      }
    }

    this.client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY,
      }
    });
  }

  isConfigured(): boolean {
    return !!(
      process.env.AZURE_OPENAI_API_KEY &&
      process.env.AZURE_OPENAI_ENDPOINT &&
      process.env.AZURE_OPENAI_API_VERSION
    );
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('azure').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const modelConfig = ModelRegistry.getModel(request.model, 'azure');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for Azure provider`);
    }

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel, // Azure deployment name
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from Azure OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
```

### Steg 5: Implementera Provider Factory

**Fil: `backend/src/services/llm/LLMProviderFactory.ts`**
```typescript
import { LLMProvider } from './interfaces';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AzureOpenAIProvider } from './providers/AzureOpenAIProvider';

export type ProviderType = 'openai' | 'azure';

export class LLMProviderFactory {
  private static instance: LLMProvider | null = null;
  private static currentProviderType: ProviderType | null = null;

  static createProvider(type: ProviderType): LLMProvider {
    switch (type) {
      case 'openai':
        return new OpenAIProvider();
      case 'azure':
        return new AzureOpenAIProvider();
      default:
        throw new Error(`Unsupported LLM provider: ${type}`);
    }
  }

  static getInstance(): LLMProvider {
    const providerType = (process.env.LLM_PROVIDER as ProviderType) || 'openai';

    // Återanvänd instans om samma provider-typ
    if (this.instance && this.currentProviderType === providerType) {
      return this.instance;
    }

    // Skapa ny instans
    this.instance = this.createProvider(providerType);
    this.currentProviderType = providerType;

    return this.instance;
  }

  static getDefaultModel(): string {
    return process.env.LLM_DEFAULT_MODEL || 'gpt-4o-mini';
  }

  static getFallbackModel(): string {
    return process.env.LLM_FALLBACK_MODEL || 'gpt-4o-mini';
  }

  static reset(): void {
    this.instance = null;
    this.currentProviderType = null;
  }
}
```

### Steg 6: Skapa LLM Service

**Fil: `backend/src/services/llm/LLMService.ts`**
```typescript
import { LLMProviderFactory } from './LLMProviderFactory';
import { ChatCompletionRequest, ChatCompletionResponse, ChatMessage } from './interfaces';
import { ModelRegistry } from './ModelRegistry';

export class LLMService {
  static async createChatCompletion(
    messages: ChatMessage[],
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): Promise<ChatCompletionResponse> {
    const provider = LLMProviderFactory.getInstance();

    if (!provider.isConfigured()) {
      throw new Error(`LLM provider ${provider.name} is not properly configured`);
    }

    const model = options.model || LLMProviderFactory.getDefaultModel();

    // Validera att modellen stöds av providern
    const supportedModels = provider.getSupportedModels();
    if (!supportedModels.includes(model)) {
      console.warn(`Model ${model} not supported by ${provider.name}, using fallback`);
      const fallbackModel = LLMProviderFactory.getFallbackModel();
      if (!supportedModels.includes(fallbackModel)) {
        throw new Error(`Neither default model ${model} nor fallback model ${fallbackModel} is supported by ${provider.name}`);
      }
      options.model = fallbackModel;
    }

    const request: ChatCompletionRequest = {
      model,
      messages,
      temperature: options.temperature,
      maxTokens: options.maxTokens
    };

    try {
      return await provider.createChatCompletion(request);
    } catch (error) {
      console.error(`LLM request failed with ${provider.name}:`, error);
      throw error;
    }
  }

  static async testConnection(): Promise<{ success: boolean; provider: string; error?: string }> {
    try {
      const provider = LLMProviderFactory.getInstance();
      const success = await provider.testConnection();
      return { success, provider: provider.name };
    } catch (error) {
      const provider = LLMProviderFactory.getInstance();
      return {
        success: false,
        provider: provider.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  static getAvailableModels(): Array<{ id: string; displayName: string; provider: string }> {
    const provider = LLMProviderFactory.getInstance();
    const supportedModelIds = provider.getSupportedModels();

    return ModelRegistry.getAllModels()
      .filter(model =>
        model.provider === provider.name &&
        supportedModelIds.includes(model.id)
      )
      .map(model => ({
        id: model.id,
        displayName: model.displayName,
        provider: model.provider
      }));
  }
}
```

### Steg 7: Uppdatera AI Assistant API

**Fil: `backend/src/api/ai-assistant.ts` - Ändringar**

**VIKTIGT: Ta bort all gammal OpenAI kod och ersätt med LLMService**

```typescript
// TA BORT dessa rader:
import OpenAI from 'openai';
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// LÄGG TILL istället:
import { LLMService } from '../services/llm/LLMService';

// TA BORT alla direkta openai.chat.completions.create() anrop
// ERSÄTT med LLMService.createChatCompletion()

// Före (TA BORT):
const completion = await openai.chat.completions.create({
  model: 'gpt-4o-mini',
  messages: [
    { role: 'system', content: SYSTEM_PROMPT },
    { role: 'user', content: `Skapa ett komplett RPA-flöde...` }
  ],
  temperature: 0.3,
  max_tokens: 2000
});
const aiResponse = completion.choices[0]?.message?.content;

// Efter (LÄGG TILL):
const completion = await LLMService.createChatCompletion([
  { role: 'system', content: SYSTEM_PROMPT },
  { role: 'user', content: `Skapa ett komplett RPA-flöde...` }
], {
  temperature: 0.3,
  maxTokens: 2000
});
const aiResponse = completion.content;

// TA BORT alla API key checks:
if (!process.env.OPENAI_API_KEY) {
  throw createError('OpenAI API key not configured', 500);
}

// ERSÄTT health endpoint för att använda LLMService:
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const healthCheck = await LLMService.testConnection();

  const response: ApiResponse = {
    success: true,
    data: {
      aiStatus: healthCheck.success ? 'connected' : 'error',
      provider: healthCheck.provider,
      hasApiKey: healthCheck.success,
      error: healthCheck.error,
      timestamp: new Date().toISOString()
    },
    message: 'AI Assistant health check'
  };

  res.json(response);
}));
```

### Steg 8: Uppdatera AIRunner

**Fil: `backend/src/runners/ai/AIRunner.ts` - Ändringar**

**VIKTIGT: Ta bort all OpenAI-specifik kod från AIRunner**

```typescript
// TA BORT dessa rader:
import OpenAI from 'openai';

export class AIRunner extends BaseRunner {
  private openai: OpenAI;

  constructor() {
    super();
    // TA BORT denna kod:
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    } else {
      this.openai = {} as OpenAI;
    }
  }

// ERSÄTT med:
export class AIRunner extends BaseRunner {
  constructor() {
    super();
    // Ingen OpenAI-specifik kod behövs här längre
  }

// TA BORT alla referenser till this.openai i executeStep
// Uppdatera executor context för att inte skicka openai-instans:

// Före (TA BORT):
const executorContext = {
  variables: context.variables,
  onLog,
  interpolateVariables: this.interpolateVariables.bind(this),
  openai: this.openai  // TA BORT denna rad
};

// Efter (BEHÅLL):
const executorContext = {
  variables: context.variables,
  onLog,
  interpolateVariables: this.interpolateVariables.bind(this)
  // openai tas bort - step executors använder LLMService direkt
};
```

### Steg 9: Uppdatera Step Executors

**Fil: `backend/src/runners/ai/stepExecutors/llmProcessing.ts` - Ändringar**

**VIKTIGT: Ta bort all OpenAI-specifik kod från step executors**

```typescript
// TA BORT openai parameter från executor context interface:
interface LlmProcessingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: ExecutionLog) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  // openai: OpenAI; // TA BORT denna rad
}

// LÄGG TILL import för LLMService:
import { LLMService } from '../../../services/llm/LLMService';

// TA BORT alla API key checks:
if (!process.env.OPENAI_API_KEY) {
  throw new Error('OpenAI API key not configured');
}

// TA BORT openai från context destructuring:
// Före:
const { variables, onLog, interpolateVariables, openai } = context;

// Efter:
const { variables, onLog, interpolateVariables } = context;

// ERSÄTT alla openai.chat.completions.create() anrop:
// Före (TA BORT):
const completion = await openai.chat.completions.create({
  model: step.model || 'gpt-4o-mini',
  messages: [...],
  temperature: step.temperature || 0.3,
  max_tokens: step.maxTokens || 2000
});
const aiResponse = completion.choices[0]?.message?.content;

// Efter (LÄGG TILL):
const completion = await LLMService.createChatCompletion([...], {
  model: step.model, // Använd LLMProviderFactory.getDefaultModel() om step.model är undefined
  temperature: step.temperature || 0.3,
  maxTokens: step.maxTokens || 2000
});
const aiResponse = completion.content;
```

**Fil: `backend/src/runners/api/APIRunner.ts` - Ändringar**

**TA BORT all OpenAI-specifik kod från APIRunner också:**

```typescript
// TA BORT dessa rader:
import OpenAI from 'openai';

export class APIRunner extends BaseRunner {
  private openai: OpenAI;

  constructor() {
    super();
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required for API runner');
    }
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

// ERSÄTT med:
export class APIRunner extends BaseRunner {
  constructor() {
    super();
    // Ingen OpenAI-specifik kod behövs
  }

// Uppdatera alla LLM-anrop att använda LLMService istället för this.openai
```

### Steg 10: Uppdatera Konfiguration

**Fil: `backend/.env.example` - Lägg till**

```bash
# LLM Provider Configuration
LLM_PROVIDER=openai                   # 'openai' | 'azure'
LLM_DEFAULT_MODEL=gpt-4o-mini        # Default model for all AI calls
LLM_FALLBACK_MODEL=gpt-4o-mini       # Fallback if default fails

# OpenAI Configuration (existing)
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration (new)
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure Model Deployments
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
AZURE_MODEL_O1_MINI=o1-mini-deployment
```

## Migration Checklist

### Förberedelser
- [x] Backup av nuvarande kod
- [x] Skapa feature branch: `feature/llm-provider-abstraction`
- [x] Dokumentera nuvarande OpenAI usage

### Implementation
- [x] Skapa LLM interfaces och base classes
- [x] Implementera ModelRegistry
- [x] Implementera OpenAIProvider
- [x] Implementera AzureOpenAIProvider
- [x] Skapa LLMProviderFactory
- [x] Skapa LLMService
- [x] Uppdatera AI Assistant API
- [x] Uppdatera AIRunner
- [x] Uppdatera APIRunner (Fortnox)
- [x] Uppdatera step executors
- [x] Uppdatera konfigurationsfiler

### Cleanup (Ta bort gammal kod)
- [x] Ta bort direkta OpenAI imports från alla filer
- [x] Ta bort hårdkodade OpenAI client instantieringar
- [x] Ta bort hårdkodade modellnamn (gpt-4o-mini)
- [x] Rensa bort oanvända OpenAI-relaterade funktioner
- [x] Verifiera att ingen dubbel kod finns kvar
- [x] Ta bort openai parameter från executor context interfaces
- [x] Ta bort API key validering från individuella komponenter
- [x] Rensa imports som inte längre används

### Testing
- [ ] Testa OpenAI provider med befintlig konfiguration
- [ ] Testa Azure provider med test-konfiguration
- [ ] Testa modellbyte (gpt-4o-mini → o1-mini)
- [ ] Testa provider-byte (openai → azure)
- [ ] Testa felhantering och fallbacks
- [ ] Verifiera att alla AI-funktioner fungerar

### Documentation
- [ ] Uppdatera README med nya konfigurationsalternativ
- [ ] Dokumentera modellkonfiguration
- [ ] Skapa troubleshooting guide
- [ ] Uppdatera API dokumentation

### Deployment
- [ ] Merge till main branch
- [ ] Uppdatera production environment variables
- [ ] Deploy och övervaka

## Fördelar med denna arkitektur

1. **Enkelt modellbyte**: Ändra bara `LLM_DEFAULT_MODEL` miljövariabel
2. **Enkelt providerbyte**: Ändra bara `LLM_PROVIDER` miljövariabel
3. **Centraliserad modellhantering**: ModelRegistry hanterar alla modellkonfigurationer
4. **Framtidssäker**: Lätt att lägga till nya providers och modeller
5. **Konsistent API**: Alla komponenter använder samma LLMService
6. **Robust felhantering**: Fallback-modeller och provider-validering
7. **Testbarhet**: Möjligt att mocka LLMService för tester

## Risker och Mitigering

### Risk: Azure deployment names skiljer sig från modellnamn
**Mitigering**: ModelRegistry mappar generiska modell-ID till provider-specifika namn

### Risk: Olika providers har olika capabilities
**Mitigering**: ModelConfig innehåller metadata om varje modells capabilities

### Risk: Breaking changes i befintlig funktionalitet
**Mitigering**: Behåll OpenAI som default, gradvis migration

### Risk: Konfigurationskomplexitet
**Mitigering**: Sane defaults och tydlig dokumentation

## Exempel på användning efter migration

### Byta från OpenAI till Azure
```bash
# Före
LLM_PROVIDER=openai
OPENAI_API_KEY=sk-...

# Efter
LLM_PROVIDER=azure
AZURE_OPENAI_API_KEY=your_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
```

### Byta modell från gpt-4o-mini till o1-mini
```bash
# Ändra bara denna rad
LLM_DEFAULT_MODEL=o1-mini
```

### Lägga till ny modell
```typescript
// I ModelRegistry.ts
{
  id: 'gpt-4-turbo',
  displayName: 'GPT-4 Turbo',
  provider: 'openai',
  providerModel: 'gpt-4-turbo-preview',
  maxTokens: 128000,
  supportsStreaming: true,
  costPer1kTokens: 0.01
}
```

Denna arkitektur ger maximal flexibilitet för att byta både provider och modell med minimal kodändring!

## Detaljerad Cleanup Checklist

### Filer som MÅSTE rensas från gammal OpenAI kod:

#### 1. `backend/src/api/ai-assistant.ts`
- [x] Ta bort: `import OpenAI from 'openai';`
- [x] Ta bort: `const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });`
- [x] Ta bort alla: `if (!process.env.OPENAI_API_KEY)` checks
- [x] Ersätt alla: `openai.chat.completions.create()` med `LLMService.createChatCompletion()`
- [x] Ta bort: `completion.choices[0]?.message?.content` och använd `completion.content`
- [x] Uppdatera health endpoint att använda `LLMService.testConnection()`

#### 2. `backend/src/runners/ai/AIRunner.ts`
- [x] Ta bort: `import OpenAI from 'openai';`
- [x] Ta bort: `private openai: OpenAI;` property
- [x] Ta bort all OpenAI client instantiering i constructor
- [x] Ta bort: `openai: this.openai` från executor context
- [x] Ta bort alla API key checks

#### 3. `backend/src/runners/api/APIRunner.ts`
- [x] Ta bort: `import OpenAI from 'openai';`
- [x] Ta bort: `private openai: OpenAI;` property
- [x] Ta bort all OpenAI client instantiering i constructor
- [x] Ersätt alla LLM-anrop med `LLMService.createChatCompletion()`
- [x] Ta bort API key requirement från constructor

#### 4. `backend/src/runners/ai/stepExecutors/llmProcessing.ts`
- [x] Ta bort: `openai: OpenAI` från `LlmProcessingExecutorContext` interface
- [x] Ta bort: `openai` från context destructuring
- [x] Ta bort alla: `if (!process.env.OPENAI_API_KEY)` checks
- [x] Ersätt alla: `openai.chat.completions.create()` med `LLMService.createChatCompletion()`
- [x] Ta bort: `completion.choices[0]?.message?.content` och använd `completion.content`

#### 5. Alla andra filer som importerar OpenAI direkt
- [x] Sök igenom hela codebase efter `import OpenAI` och ersätt med LLMService
- [x] Sök efter `new OpenAI(` och ta bort alla direkta instantieringar
- [x] Sök efter `process.env.OPENAI_API_KEY` och ta bort individuella checks
- [x] Sök efter `gpt-4o-mini` hårdkodade referenser och ersätt med konfigurerbara värden

### Validering efter cleanup:
- [x] Kör `grep -r "import OpenAI" backend/src/` - ska returnera 0 resultat (förutom i providers) ✅ VERIFIERAT
- [x] Kör `grep -r "new OpenAI(" backend/src/` - ska returnera 0 resultat (förutom i providers) ✅ VERIFIERAT
- [x] Kör `grep -r "process.env.OPENAI_API_KEY" backend/src/` - ska bara finnas i OpenAIProvider ✅ VERIFIERAT
- [x] Kör `grep -r "gpt-4o-mini" backend/src/` - ska bara finnas i ModelRegistry och default configs ✅ VERIFIERAT
- [ ] Verifiera att alla tester fortfarande passerar
- [ ] Verifiera att AI Assistant fungerar med nya arkitekturen

### Viktiga principer för cleanup:
1. **Ingen dubbel kod** - Ta bort ALL gammal OpenAI-specifik kod
2. **Centraliserad hantering** - Alla LLM-anrop ska gå via LLMService
3. **Ingen hårdkodning** - Inga direkta referenser till OpenAI eller specifika modeller
4. **Ren separation** - Provider-specifik kod ska bara finnas i provider-klasser

---

## 🎉 MIGRATION STATUS: FULLSTÄNDIGT GENOMFÖRD

**Datum för slutförande:** 2025-01-14

### ✅ Vad som är KLART:
- **Implementation:** All kod är implementerad enligt planen
- **Cleanup:** All gammal OpenAI-kod är borttagen från rätt ställen
- **Konfiguration:** .env.example är uppdaterad med nya LLM-inställningar
- **Validering:** Alla sökningar bekräftar korrekt cleanup
- **Arkitektur:** Systemet stöder nu enkelt byte mellan providers och modeller

### 🔄 Vad som ÅTERSTÅR:
- **Testing:** Funktionalitetstester med både OpenAI och Azure providers
- **Documentation:** Uppdatera användarguider och README-filer
- **Deployment:** Produktionsdeploy när testning är klar

### 🚀 Nästa steg:
1. Testa AI Assistant med nuvarande OpenAI-konfiguration
2. Testa provider-byte till Azure OpenAI
3. Testa modellbyte (gpt-4o-mini → o1-mini)
4. Uppdatera dokumentation
5. Deploy till production

**Migration genomförd enligt plan - redo för testning och deployment!**
